/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package watcher

import (
	"os"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformFilesCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
)

// ISuspendedWatchRequest represents a suspended watch request
type ISuspendedWatchRequest struct {
	ID            int    `json:"id"`
	CorrelationID *int   `json:"correlationId"`
	Path          string `json:"path"`
}

// correlatedRequestWrapper wraps a correlated request to implement IUniversalWatchRequest
type correlatedRequestWrapper struct {
	request platformFilesCommon.IWatchRequestWithCorrelation
}

func (w *correlatedRequestWrapper) GetPath() string {
	return w.request.Path
}

func (w *correlatedRequestWrapper) GetRecursive() bool {
	return w.request.Recursive
}

func (w *correlatedRequestWrapper) GetExcludes() []string {
	return w.request.Excludes
}

func (w *correlatedRequestWrapper) GetIncludes() []interface{} {
	return w.request.Includes
}

func (w *correlatedRequestWrapper) GetCorrelationId() *int {
	return &w.request.CorrelationId
}

func (w *correlatedRequestWrapper) GetFilter() *platformFilesCommon.FileChangeFilter {
	return w.request.Filter
}

// BaseWatcher is an abstract base class for file watchers
type BaseWatcher struct {
	*baseCommon.Disposable

	// Events
	onDidChangeFile *baseCommon.Emitter[[]platformFilesCommon.IFileChange]
	onDidLogMessage *baseCommon.Emitter[platformFilesCommon.ILogMessage]
	onDidWatchFail  *baseCommon.Emitter[platformFilesCommon.IUniversalWatchRequest]

	// Internal state
	correlatedWatchRequests    map[int]platformFilesCommon.IWatchRequestWithCorrelation
	nonCorrelatedWatchRequests map[int]platformFilesCommon.IUniversalWatchRequest

	suspendedWatchRequests            *baseCommon.DisposableMap[int, baseCommon.IDisposable]
	suspendedWatchRequestsWithPolling map[int]bool

	updateWatchersDelayer *baseCommon.ThrottledDelayer[interface{}]

	suspendedWatchRequestPollingInterval time.Duration

	joinWatch *baseCommon.DeferredPromise[interface{}]

	verboseLogging bool

	mu sync.RWMutex
}

// NewBaseWatcher creates a new BaseWatcher
func NewBaseWatcher() *BaseWatcher {
	bw := &BaseWatcher{
		Disposable:                           baseCommon.NewDisposable(),
		onDidChangeFile:                      baseCommon.NewEmitter[[]platformFilesCommon.IFileChange](),
		onDidLogMessage:                      baseCommon.NewEmitter[platformFilesCommon.ILogMessage](),
		onDidWatchFail:                       baseCommon.NewEmitter[platformFilesCommon.IUniversalWatchRequest](),
		correlatedWatchRequests:              make(map[int]platformFilesCommon.IWatchRequestWithCorrelation),
		nonCorrelatedWatchRequests:           make(map[int]platformFilesCommon.IUniversalWatchRequest),
		suspendedWatchRequests:               baseCommon.NewDisposableMap[int, baseCommon.IDisposable](),
		suspendedWatchRequestsWithPolling:    make(map[int]bool),
		updateWatchersDelayer:                baseCommon.NewThrottledDelayer[interface{}](800 * time.Millisecond),
		suspendedWatchRequestPollingInterval: 5007 * time.Millisecond, // node.js default
		joinWatch:                            baseCommon.NewDeferredPromise[interface{}](),
		verboseLogging:                       false,
	}

	// Register the watcher delayer for disposal
	bw.Register(bw.updateWatchersDelayer)
	bw.Register(bw.suspendedWatchRequests)

	// Set up watch fail handler
	bw.Register(bw.onDidWatchFail.Subscribe(func(request platformFilesCommon.IUniversalWatchRequest) {
		bw.suspendWatchRequest(&ISuspendedWatchRequest{
			ID:            bw.computeID(request),
			CorrelationID: bw.getCorrelationID(request),
			Path:          request.GetPath(),
		})
	}))

	return bw
}

// OnDidChangeFile returns the file change event
func (bw *BaseWatcher) OnDidChangeFile() baseCommon.Event[[]platformFilesCommon.IFileChange] {
	return bw.onDidChangeFile
}

// OnDidLogMessage returns the log message event
func (bw *BaseWatcher) OnDidLogMessage() baseCommon.Event[platformFilesCommon.ILogMessage] {
	return bw.onDidLogMessage
}

// isCorrelated checks if a request is correlated
func (bw *BaseWatcher) isCorrelated(request platformFilesCommon.IUniversalWatchRequest) bool {
	return request.GetCorrelationId() != nil
}

// computeID computes an ID for a watch request
func (bw *BaseWatcher) computeID(request platformFilesCommon.IUniversalWatchRequest) int {
	if bw.isCorrelated(request) {
		if correlationID := request.GetCorrelationId(); correlationID != nil {
			return *correlationID
		}
	}

	// Requests without correlation do not carry any unique identifier, so we have to
	// come up with one based on the options of the request. This matches what the
	// file service does (vs/platform/files/common/fileService.ts#L1178).
	return baseCommon.Hash(request)
}

// getCorrelationID gets the correlation ID from a request
func (bw *BaseWatcher) getCorrelationID(request platformFilesCommon.IUniversalWatchRequest) *int {
	if bw.isCorrelated(request) {
		return request.GetCorrelationId()
	}
	return nil
}

// Watch starts watching the given requests
func (bw *BaseWatcher) Watch(requests []platformFilesCommon.IUniversalWatchRequest) error {
	bw.mu.Lock()
	defer bw.mu.Unlock()

	if !bw.joinWatch.IsSettled() {
		bw.joinWatch.Complete(nil)
	}
	bw.joinWatch = baseCommon.NewDeferredPromise[interface{}]()

	defer func() {
		bw.joinWatch.Complete(nil)
	}()

	// Clear existing requests
	bw.correlatedWatchRequests = make(map[int]platformFilesCommon.IWatchRequestWithCorrelation)
	bw.nonCorrelatedWatchRequests = make(map[int]platformFilesCommon.IUniversalWatchRequest)

	// Figure out correlated vs. non-correlated requests
	for _, request := range requests {
		id := bw.computeID(request)
		if bw.isCorrelated(request) {
			if correlationID := request.GetCorrelationId(); correlationID != nil {
				// Convert to correlated request
				correlatedReq := platformFilesCommon.IWatchRequestWithCorrelation{
					IWatchRequest: platformFilesCommon.IWatchRequest{
						Path:          request.GetPath(),
						Recursive:     request.GetRecursive(),
						Excludes:      request.GetExcludes(),
						Includes:      request.GetIncludes(),
						CorrelationId: correlationID,
						Filter:        request.GetFilter(),
					},
					CorrelationId: *correlationID,
				}
				bw.correlatedWatchRequests[*correlationID] = correlatedReq
			}
		} else {
			bw.nonCorrelatedWatchRequests[id] = request
		}
	}

	// Remove all suspended watch requests that are no longer watched
	// Note: We need to collect IDs first to avoid modifying map while iterating
	var suspendedIDs []int
	for id := range bw.suspendedWatchRequestsWithPolling {
		suspendedIDs = append(suspendedIDs, id)
	}

	for _, id := range suspendedIDs {
		_, hasCorrelated := bw.correlatedWatchRequests[id]
		_, hasNonCorrelated := bw.nonCorrelatedWatchRequests[id]
		if !hasCorrelated && !hasNonCorrelated {
			bw.suspendedWatchRequests.DeleteAndDispose(id)
			delete(bw.suspendedWatchRequestsWithPolling, id)
		}
	}

	return bw.updateWatchers(false)
}

// updateWatchers updates the watchers
func (bw *BaseWatcher) updateWatchers(delayed bool) error {
	bw.mu.RLock()
	nonSuspendedRequests := make([]platformFilesCommon.IUniversalWatchRequest, 0)

	// Collect non-suspended requests
	for id, request := range bw.nonCorrelatedWatchRequests {
		if !bw.suspendedWatchRequests.Has(id) {
			nonSuspendedRequests = append(nonSuspendedRequests, request)
		}
	}
	for id, request := range bw.correlatedWatchRequests {
		if !bw.suspendedWatchRequests.Has(id) {
			// Convert correlated request to universal request interface
			wrapper := &correlatedRequestWrapper{request}
			nonSuspendedRequests = append(nonSuspendedRequests, wrapper)
		}
	}
	bw.mu.RUnlock()

	delay := time.Duration(0)
	if delayed {
		delay = bw.getUpdateWatchersDelay()
	}

	// Trigger the delayer
	result := bw.updateWatchersDelayer.Trigger(func() <-chan interface{} {
		resultChan := make(chan interface{}, 1)
		go func() {
			err := bw.doWatch(nonSuspendedRequests)
			if err != nil {
				baseCommon.OnUnexpectedError(err)
			}
			resultChan <- nil
		}()
		return resultChan
	}, delay)

	// Wait for result (simplified)
	<-result
	return nil
}

// getUpdateWatchersDelay returns the delay for updating watchers
func (bw *BaseWatcher) getUpdateWatchersDelay() time.Duration {
	return 800 * time.Millisecond
}

// IsSuspended checks if a request is suspended
func (bw *BaseWatcher) IsSuspended(request platformFilesCommon.IUniversalWatchRequest) interface{} {
	bw.mu.RLock()
	defer bw.mu.RUnlock()

	id := bw.computeID(request)
	if bw.suspendedWatchRequestsWithPolling[id] {
		return "polling"
	}
	return bw.suspendedWatchRequests.Has(id)
}

// suspendWatchRequest suspends a watch request
func (bw *BaseWatcher) suspendWatchRequest(request *ISuspendedWatchRequest) {
	bw.mu.Lock()
	defer bw.mu.Unlock()

	if bw.suspendedWatchRequests.Has(request.ID) {
		return // already suspended
	}

	disposables := baseCommon.NewDisposableStore()
	bw.suspendedWatchRequests.Set(request.ID, disposables)

	// Wait for all watch requests to be processed
	go func() {
		<-bw.joinWatch.Promise()

		if disposables.IsDisposed() {
			return
		}

		bw.monitorSuspendedWatchRequest(request, disposables)
		bw.updateWatchers(true) // delay this call as we might accumulate many failing watch requests on startup
	}()
}

// resumeWatchRequest resumes a suspended watch request
func (bw *BaseWatcher) resumeWatchRequest(request *ISuspendedWatchRequest) {
	bw.mu.Lock()
	defer bw.mu.Unlock()

	bw.suspendedWatchRequests.DeleteAndDispose(request.ID)
	delete(bw.suspendedWatchRequestsWithPolling, request.ID)

	bw.updateWatchers(false)
}

// monitorSuspendedWatchRequest monitors a suspended watch request
func (bw *BaseWatcher) monitorSuspendedWatchRequest(request *ISuspendedWatchRequest, disposables *baseCommon.DisposableStore) {
	if bw.doMonitorWithExistingWatcher(request, disposables) {
		bw.trace("reusing an existing recursive watcher to monitor " + request.Path)
		delete(bw.suspendedWatchRequestsWithPolling, request.ID)
	} else {
		bw.doMonitorWithNodeJS(request, disposables)
		bw.suspendedWatchRequestsWithPolling[request.ID] = true
	}
}

// doMonitorWithExistingWatcher tries to monitor with an existing recursive watcher
func (bw *BaseWatcher) doMonitorWithExistingWatcher(request *ISuspendedWatchRequest, disposables *baseCommon.DisposableStore) bool {
	recursiveWatcher := bw.getRecursiveWatcher()
	if recursiveWatcher == nil {
		return false
	}

	subscription := recursiveWatcher.Subscribe(request.Path, func(error bool, change *platformFilesCommon.IFileChange) {
		if disposables.IsDisposed() {
			return // return early if already disposed
		}

		if error {
			bw.monitorSuspendedWatchRequest(request, disposables)
		} else if change != nil && change.Type == platformFilesCommon.FileChangeTypeADDED {
			bw.onMonitoredPathAdded(request)
		}
	})

	if subscription != nil {
		disposables.Add(subscription)
		return true
	}

	return false
}

// doMonitorWithNodeJS monitors using Node.js fs.watchFile equivalent (using fsnotify)
func (bw *BaseWatcher) doMonitorWithNodeJS(request *ISuspendedWatchRequest, disposables *baseCommon.DisposableStore) {
	pathNotFound := false

	// Create a file watcher
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		bw.warn("failed to create file watcher for " + request.Path + ": " + err.Error())
		return
	}

	// Add the path to watch
	err = watcher.Add(request.Path)
	if err != nil {
		bw.warn("failed to watch path " + request.Path + ": " + err.Error())
		watcher.Close()
		return
	}

	bw.trace("starting fsnotify watcher on " + request.Path + " (correlationId: " + bw.formatCorrelationID(request.CorrelationID) + ")")

	// Start monitoring in a goroutine
	go func() {
		defer watcher.Close()

		// Set up polling timer
		ticker := time.NewTicker(bw.suspendedWatchRequestPollingInterval)
		defer ticker.Stop()

		for {
			select {
			case event, ok := <-watcher.Events:
				if !ok {
					return
				}
				if disposables.IsDisposed() {
					return
				}

				// Check if file was created
				if event.Op&fsnotify.Create == fsnotify.Create {
					if pathNotFound {
						bw.onMonitoredPathAdded(request)
						return
					}
				}

			case err, ok := <-watcher.Errors:
				if !ok {
					return
				}
				if disposables.IsDisposed() {
					return
				}
				bw.warn("fsnotify error for " + request.Path + ": " + err.Error())

			case <-ticker.C:
				if disposables.IsDisposed() {
					return
				}

				// Check if path exists
				_, err := os.Stat(request.Path)
				currentPathNotFound := os.IsNotExist(err)

				// Watch path created: resume watching request
				if !currentPathNotFound && pathNotFound {
					bw.onMonitoredPathAdded(request)
					return
				}

				pathNotFound = currentPathNotFound
			}
		}
	}()

	// Add cleanup to disposables
	disposables.Add(baseCommon.ToDisposable(func() {
		bw.trace("stopping fsnotify watcher on " + request.Path + " (correlationId: " + bw.formatCorrelationID(request.CorrelationID) + ")")
		watcher.Close()
	}))
}

// onMonitoredPathAdded handles when a monitored path is added
func (bw *BaseWatcher) onMonitoredPathAdded(request *ISuspendedWatchRequest) {
	bw.trace("detected " + request.Path + " exists again, resuming watcher (correlationId: " + bw.formatCorrelationID(request.CorrelationID) + ")")

	// Emit as event
	event := platformFilesCommon.IFileChange{
		Resource: baseCommon.File(request.Path),
		Type:     platformFilesCommon.FileChangeTypeADDED,
		CId:      request.CorrelationID,
	}
	bw.onDidChangeFile.Fire([]platformFilesCommon.IFileChange{event})
	bw.traceEvent(&event, request)

	// Resume watching
	bw.resumeWatchRequest(request)
}

// formatCorrelationID formats a correlation ID for logging
func (bw *BaseWatcher) formatCorrelationID(correlationID *int) string {
	if correlationID != nil {
		return string(rune(*correlationID))
	}
	return "<none>"
}

// Stop stops the watcher
func (bw *BaseWatcher) Stop() error {
	bw.mu.Lock()
	defer bw.mu.Unlock()

	bw.suspendedWatchRequests.ClearAndDisposeAll()
	bw.suspendedWatchRequestsWithPolling = make(map[int]bool)
	return nil
}

// traceEvent traces a file change event
func (bw *BaseWatcher) traceEvent(event *platformFilesCommon.IFileChange, request interface{}) {
	if bw.verboseLogging {
		var traceMsg string
		switch event.Type {
		case platformFilesCommon.FileChangeTypeADDED:
			traceMsg = " >> normalized [ADDED] " + event.Resource.GetFSPath()
		case platformFilesCommon.FileChangeTypeDELETED:
			traceMsg = " >> normalized [DELETED] " + event.Resource.GetFSPath()
		case platformFilesCommon.FileChangeTypeUPDATED:
			traceMsg = " >> normalized [CHANGED] " + event.Resource.GetFSPath()
		}
		bw.traceWithCorrelation(traceMsg, request)
	}
}

// traceWithCorrelation traces a message with correlation information
func (bw *BaseWatcher) traceWithCorrelation(message string, request interface{}) {
	if bw.verboseLogging {
		correlationSuffix := ""
		if req, ok := request.(*ISuspendedWatchRequest); ok && req.CorrelationID != nil {
			correlationSuffix = " <" + bw.formatCorrelationID(req.CorrelationID) + "> "
		}
		bw.trace(message + correlationSuffix)
	}
}

// requestToString converts a request to string representation
func (bw *BaseWatcher) requestToString(request platformFilesCommon.IUniversalWatchRequest) string {
	excludes := "<none>"
	if len(request.GetExcludes()) > 0 {
		excludes = ""
		for i, exclude := range request.GetExcludes() {
			if i > 0 {
				excludes += ", "
			}
			excludes += exclude
		}
	}

	includes := "<all>"
	if request.GetIncludes() != nil && len(request.GetIncludes()) > 0 {
		includes = ""
		for i, include := range request.GetIncludes() {
			if i > 0 {
				includes += ", "
			}
			includes += include.(string) // Simplified conversion
		}
	}

	filter := platformFilesCommon.RequestFilterToString(request.GetFilter())
	correlationID := "<none>"
	if request.GetCorrelationId() != nil {
		correlationID = bw.formatCorrelationID(request.GetCorrelationId())
	}

	return request.GetPath() + " (excludes: " + excludes + ", includes: " + includes + ", filter: " + filter + ", correlationId: " + correlationID + ")"
}

// SetVerboseLogging sets verbose logging
func (bw *BaseWatcher) SetVerboseLogging(enabled bool) error {
	bw.mu.Lock()
	defer bw.mu.Unlock()
	bw.verboseLogging = enabled
	return nil
}

// Abstract methods that must be implemented by subclasses

// doWatch performs the actual watching (abstract method)
func (bw *BaseWatcher) doWatch(requests []platformFilesCommon.IUniversalWatchRequest) error {
	panic("doWatch must be implemented by subclass")
}

// getRecursiveWatcher returns the recursive watcher (abstract method)
func (bw *BaseWatcher) getRecursiveWatcher() platformFilesCommon.IRecursiveWatcherWithSubscribe {
	return nil // Default implementation returns nil
}

// trace logs a trace message (abstract method)
func (bw *BaseWatcher) trace(message string) {
	// Default implementation does nothing
	// Subclasses should override this
}

// warn logs a warning message (abstract method)
func (bw *BaseWatcher) warn(message string) {
	// Default implementation does nothing
	// Subclasses should override this
}

// OnDidError returns the error event (abstract method)
func (bw *BaseWatcher) OnDidError() baseCommon.Event[platformFilesCommon.IWatcherErrorEvent] {
	panic("OnDidError must be implemented by subclass")
}
