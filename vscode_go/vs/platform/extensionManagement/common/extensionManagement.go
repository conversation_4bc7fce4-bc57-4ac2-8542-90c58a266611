/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"regexp"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	extensionscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensions/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userdataprofilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

const (
	EXTENSION_IDENTIFIER_PATTERN                     = `^([a-z0-9A-Z][a-z0-9-A-Z]*)\\.([a-z0-9A-Z][a-z0-9-A-Z]*)$`
	WEB_EXTENSION_TAG                                = "__web_extension"
	EXTENSION_INSTALL_SKIP_WALKTHROUGH_CONTEXT       = "skipWalkthrough"
	EXTENSION_INSTALL_SKIP_PUBLISHER_TRUST_CONTEXT   = "skipPublisherTrust"
	EXTENSION_INSTALL_SOURCE_CONTEXT                 = "extensionInstallSource"
	EXTENSION_INSTALL_DEP_PACK_CONTEXT               = "dependecyOrPackExtensionInstall"
	EXTENSION_INSTALL_CLIENT_TARGET_PLATFORM_CONTEXT = "clientTargetPlatform"
)

var EXTENSION_IDENTIFIER_REGEX = regexp.MustCompile(EXTENSION_IDENTIFIER_PATTERN)

// ExtensionManagementErrorCode represents error codes for extension management operations
type ExtensionManagementErrorCode string

const (
	ExtensionManagementErrorCodeNotFound                      ExtensionManagementErrorCode = "NotFound"
	ExtensionManagementErrorCodeUnsupported                   ExtensionManagementErrorCode = "Unsupported"
	ExtensionManagementErrorCodeDeprecated                    ExtensionManagementErrorCode = "Deprecated"
	ExtensionManagementErrorCodeMalicious                     ExtensionManagementErrorCode = "Malicious"
	ExtensionManagementErrorCodeIncompatible                  ExtensionManagementErrorCode = "Incompatible"
	ExtensionManagementErrorCodeIncompatibleApi               ExtensionManagementErrorCode = "IncompatibleApi"
	ExtensionManagementErrorCodeIncompatibleTargetPlatform    ExtensionManagementErrorCode = "IncompatibleTargetPlatform"
	ExtensionManagementErrorCodeReleaseVersionNotFound        ExtensionManagementErrorCode = "ReleaseVersionNotFound"
	ExtensionManagementErrorCodeInvalid                       ExtensionManagementErrorCode = "Invalid"
	ExtensionManagementErrorCodeDownload                      ExtensionManagementErrorCode = "Download"
	ExtensionManagementErrorCodeDownloadSignature             ExtensionManagementErrorCode = "DownloadSignature"
	ExtensionManagementErrorCodeDownloadFailedWriting         ExtensionManagementErrorCode = "DownloadFailedWriting"
	ExtensionManagementErrorCodeUpdateMetadata                ExtensionManagementErrorCode = "UpdateMetadata"
	ExtensionManagementErrorCodeExtract                       ExtensionManagementErrorCode = "Extract"
	ExtensionManagementErrorCodeScanning                      ExtensionManagementErrorCode = "Scanning"
	ExtensionManagementErrorCodeScanningExtension             ExtensionManagementErrorCode = "ScanningExtension"
	ExtensionManagementErrorCodeReadRemoved                   ExtensionManagementErrorCode = "ReadRemoved"
	ExtensionManagementErrorCodeUnsetRemoved                  ExtensionManagementErrorCode = "UnsetRemoved"
	ExtensionManagementErrorCodeDelete                        ExtensionManagementErrorCode = "Delete"
	ExtensionManagementErrorCodeRename                        ExtensionManagementErrorCode = "Rename"
	ExtensionManagementErrorCodeIntializeDefaultProfile       ExtensionManagementErrorCode = "IntializeDefaultProfile"
	ExtensionManagementErrorCodeAddToProfile                  ExtensionManagementErrorCode = "AddToProfile"
	ExtensionManagementErrorCodeInstalledExtensionNotFound    ExtensionManagementErrorCode = "InstalledExtensionNotFound"
	ExtensionManagementErrorCodePostInstall                   ExtensionManagementErrorCode = "PostInstall"
	ExtensionManagementErrorCodeCorruptZip                    ExtensionManagementErrorCode = "CorruptZip"
	ExtensionManagementErrorCodeIncompleteZip                 ExtensionManagementErrorCode = "IncompleteZip"
	ExtensionManagementErrorCodePackageNotSigned              ExtensionManagementErrorCode = "PackageNotSigned"
	ExtensionManagementErrorCodeSignatureVerificationInternal ExtensionManagementErrorCode = "SignatureVerificationInternal"
	ExtensionManagementErrorCodeSignatureVerificationFailed   ExtensionManagementErrorCode = "SignatureVerificationFailed"
	ExtensionManagementErrorCodeNotAllowed                    ExtensionManagementErrorCode = "NotAllowed"
	ExtensionManagementErrorCodeGallery                       ExtensionManagementErrorCode = "Gallery"
	ExtensionManagementErrorCodeCancelled                     ExtensionManagementErrorCode = "Cancelled"
	ExtensionManagementErrorCodeUnknown                       ExtensionManagementErrorCode = "Unknown"
	ExtensionManagementErrorCodeInternal                      ExtensionManagementErrorCode = "Internal"
)

// ExtensionManagementError represents an error in extension management operations
type ExtensionManagementError struct {
	Message string
	Code    ExtensionManagementErrorCode
}

func (e *ExtensionManagementError) Error() string {
	return e.Message
}

// NewExtensionManagementError creates a new ExtensionManagementError
func NewExtensionManagementError(message string, code ExtensionManagementErrorCode) *ExtensionManagementError {
	return &ExtensionManagementError{
		Message: message,
		Code:    code,
	}
}

// ToExtensionManagementError converts an error to an ExtensionManagementError
func ToExtensionManagementError(err error, code ExtensionManagementErrorCode) *ExtensionManagementError {
	if extErr, ok := err.(*ExtensionManagementError); ok {
		return extErr
	}
	return NewExtensionManagementError(err.Error(), code)
}

// IScannedProfileExtension represents a scanned extension in a profile
type IScannedProfileExtension struct {
	Identifier extensionscommon.IExtensionIdentifier `json:"identifier"`
	Version    string                                `json:"version"`
	Location   basecommon.URI                        `json:"location"`
	Metadata   interface{}                           `json:"metadata,omitempty"`
}

// ProfileExtensionsEvent represents an event for profile extensions operations
type ProfileExtensionsEvent struct {
	Extensions      []IScannedProfileExtension `json:"extensions"`
	ProfileLocation basecommon.URI             `json:"profileLocation"`
}

// DidAddProfileExtensionsEvent represents completion of adding extensions to a profile
type DidAddProfileExtensionsEvent struct {
	ProfileExtensionsEvent
	Error error `json:"error,omitempty"`
}

// DidRemoveProfileExtensionsEvent represents completion of removing extensions from a profile
type DidRemoveProfileExtensionsEvent struct {
	ProfileExtensionsEvent
	Error error `json:"error,omitempty"`
}

// IProfileExtensionsScanOptions represents options for scanning profile extensions
type IProfileExtensionsScanOptions struct {
	BailOutWhenFileNotFound bool `json:"bailOutWhenFileNotFound,omitempty"`
}

// IExtensionsProfileScannerService interface for scanning extensions in profiles
type IExtensionsProfileScannerService interface {
	// Events
	OnAddExtensions() basecommon.Event[ProfileExtensionsEvent]
	OnDidAddExtensions() basecommon.Event[DidAddProfileExtensionsEvent]
	OnRemoveExtensions() basecommon.Event[ProfileExtensionsEvent]
	OnDidRemoveExtensions() basecommon.Event[DidRemoveProfileExtensionsEvent]

	// Methods
	ScanProfileExtensions(profileLocation basecommon.URI, options *IProfileExtensionsScanOptions) ([]IScannedProfileExtension, error)
	AddExtensionsToProfile(extensions []struct {
		Extension extensionscommon.IExtension
		Metadata  interface{}
	}, profileLocation basecommon.URI, keepExistingVersions bool) ([]IScannedProfileExtension, error)
	UpdateMetadata(extensions []struct {
		Extension extensionscommon.IExtension
		Metadata  interface{}
	}, profileLocation basecommon.URI) ([]IScannedProfileExtension, error)
	RemoveExtensionsFromProfile(extensions []extensionscommon.IExtensionIdentifier, profileLocation basecommon.URI) error
}

// InstallOptions represents options for installing extensions
type InstallOptions struct {
	IsBuiltin                bool              `json:"isBuiltin,omitempty"`
	IsMachineScoped          bool              `json:"isMachineScoped,omitempty"`
	DonotIncludePackAndDeps  bool              `json:"donotIncludePackAndDeps,omitempty"`
	DonotVerifySignature     bool              `json:"donotVerifySignature,omitempty"`
	InstallGivenVersion      bool              `json:"installGivenVersion,omitempty"`
	InstallPreReleaseVersion bool              `json:"installPreReleaseVersion,omitempty"`
	IsApplicationScoped      bool              `json:"isApplicationScoped,omitempty"`
	ProfileLocation          *basecommon.URI   `json:"profileLocation,omitempty"`
	ProductVersion           *IProductVersion  `json:"productVersion,omitempty"`
	Context                  map[string]string `json:"context,omitempty"`
}

// UninstallOptions represents options for uninstalling extensions
type UninstallOptions struct {
	DonotIncludePack     bool            `json:"donotIncludePack,omitempty"`
	DonotCheckDependents bool            `json:"donotCheckDependents,omitempty"`
	ProfileLocation      *basecommon.URI `json:"profileLocation,omitempty"`
}

// InstallOperation represents the type of install operation
type InstallOperation string

const (
	InstallOperationInstall InstallOperation = "install"
	InstallOperationUpdate  InstallOperation = "update"
	InstallOperationNone    InstallOperation = "none"
)

// ExtensionSignatureVerificationCode represents signature verification status
type ExtensionSignatureVerificationCode string

const (
	ExtensionSignatureVerificationCodeSuccess ExtensionSignatureVerificationCode = "Success"
	ExtensionSignatureVerificationCodeError   ExtensionSignatureVerificationCode = "Error"
	ExtensionSignatureVerificationCodeUnknown ExtensionSignatureVerificationCode = "Unknown"
)

// Event types for extension management
type InstallExtensionEvent struct {
	Identifier      extensionscommon.IExtensionIdentifier `json:"identifier"`
	Source          interface{}                           `json:"source"` // IGalleryExtension | basecommon.URI
	ProfileLocation basecommon.URI                        `json:"profileLocation"`
}

type InstallExtensionResult struct {
	Identifier      extensionscommon.IExtensionIdentifier `json:"identifier"`
	Operation       InstallOperation                      `json:"operation"`
	Source          interface{}                           `json:"source"` // IGalleryExtension | basecommon.URI
	Local           *ILocalExtension                      `json:"local,omitempty"`
	Error           error                                 `json:"error,omitempty"`
	ProfileLocation basecommon.URI                        `json:"profileLocation"`
}

type UninstallExtensionEvent struct {
	Identifier      extensionscommon.IExtensionIdentifier `json:"identifier"`
	ProfileLocation basecommon.URI                        `json:"profileLocation"`
}

type DidUninstallExtensionEvent struct {
	Identifier      extensionscommon.IExtensionIdentifier `json:"identifier"`
	Error           error                                 `json:"error,omitempty"`
	ProfileLocation basecommon.URI                        `json:"profileLocation"`
}

type DidUpdateExtensionMetadata struct {
	Identifier      extensionscommon.IExtensionIdentifier `json:"identifier"`
	Local           *ILocalExtension                      `json:"local"`
	ProfileLocation basecommon.URI                        `json:"profileLocation"`
}

// InstallExtensionInfo represents information for installing an extension
type InstallExtensionInfo struct {
	Extension *IGalleryExtension `json:"extension"`
	Options   InstallOptions     `json:"options"`
}

// UninstallExtensionInfo represents information for uninstalling an extension
type UninstallExtensionInfo struct {
	Identifier extensionscommon.IExtensionIdentifier `json:"identifier"`
	Options    UninstallOptions                      `json:"options"`
}

// IExtensionsControlManifest represents the extensions control manifest
type IExtensionsControlManifest struct {
	Malicious []string `json:"malicious"`
}

// IAllowedExtensionsService interface for managing allowed extensions
type IAllowedExtensionsService interface {
	IsAllowed(extension extensionscommon.IExtensionIdentifier) bool
}

// IExtensionGalleryService interface for gallery operations
type IExtensionGalleryService interface {
	IsEnabled() bool
	Query(options interface{}, token basecommon.CancellationToken) ([]*IGalleryExtension, error)
	Download(extension *IGalleryExtension, location basecommon.URI, operation InstallOperation) (basecommon.URI, error)
	GetManifest(extension *IGalleryExtension, token basecommon.CancellationToken) (*extensionscommon.IExtensionManifest, error)
	GetExtensionsControlManifest() (*IExtensionsControlManifest, error)
}

// MaliciousExtensionInfo represents information about malicious extensions
type MaliciousExtensionInfo struct {
	ExtensionOrPublisher struct {
		Type      string                                `json:"type"`
		Value     string                                `json:"value,omitempty"`
		Extension extensionscommon.IExtensionIdentifier `json:"extension,omitempty"`
	} `json:"extensionOrPublisher"`
}

// AbstractExtensionsProfileScannerService provides base implementation
type AbstractExtensionsProfileScannerService struct {
	*basecommon.Disposable
	onAddExtensions       *basecommon.Emitter[ProfileExtensionsEvent]
	onDidAddExtensions    *basecommon.Emitter[DidAddProfileExtensionsEvent]
	onRemoveExtensions    *basecommon.Emitter[ProfileExtensionsEvent]
	onDidRemoveExtensions *basecommon.Emitter[DidRemoveProfileExtensionsEvent]
}

// NewAbstractExtensionsProfileScannerService creates a new instance
func NewAbstractExtensionsProfileScannerService(
	extensionsLocation basecommon.URI,
	fileService filescommon.IFileService,
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	logService logcommon.ILogService,
) *AbstractExtensionsProfileScannerService {
	service := &AbstractExtensionsProfileScannerService{
		Disposable:            basecommon.NewDisposable(),
		onAddExtensions:       basecommon.NewEmitter[ProfileExtensionsEvent](),
		onDidAddExtensions:    basecommon.NewEmitter[DidAddProfileExtensionsEvent](),
		onRemoveExtensions:    basecommon.NewEmitter[ProfileExtensionsEvent](),
		onDidRemoveExtensions: basecommon.NewEmitter[DidRemoveProfileExtensionsEvent](),
	}

	service.Register(service.onAddExtensions)
	service.Register(service.onDidAddExtensions)
	service.Register(service.onRemoveExtensions)
	service.Register(service.onDidRemoveExtensions)

	return service
}

// Event methods
func (s *AbstractExtensionsProfileScannerService) OnAddExtensions() basecommon.Event[ProfileExtensionsEvent] {
	return s.onAddExtensions
}

func (s *AbstractExtensionsProfileScannerService) OnDidAddExtensions() basecommon.Event[DidAddProfileExtensionsEvent] {
	return s.onDidAddExtensions
}

func (s *AbstractExtensionsProfileScannerService) OnRemoveExtensions() basecommon.Event[ProfileExtensionsEvent] {
	return s.onRemoveExtensions
}

func (s *AbstractExtensionsProfileScannerService) OnDidRemoveExtensions() basecommon.Event[DidRemoveProfileExtensionsEvent] {
	return s.onDidRemoveExtensions
}

// Placeholder implementations - to be overridden by concrete implementations
func (s *AbstractExtensionsProfileScannerService) ScanProfileExtensions(profileLocation basecommon.URI, options *IProfileExtensionsScanOptions) ([]IScannedProfileExtension, error) {
	// Placeholder implementation
	return []IScannedProfileExtension{}, nil
}

func (s *AbstractExtensionsProfileScannerService) AddExtensionsToProfile(extensions []struct {
	Extension extensionscommon.IExtension
	Metadata  interface{}
}, profileLocation basecommon.URI, keepExistingVersions bool) ([]IScannedProfileExtension, error) {
	// Placeholder implementation
	return []IScannedProfileExtension{}, nil
}

func (s *AbstractExtensionsProfileScannerService) UpdateMetadata(extensions []struct {
	Extension extensionscommon.IExtension
	Metadata  interface{}
}, profileLocation basecommon.URI) ([]IScannedProfileExtension, error) {
	// Placeholder implementation
	return []IScannedProfileExtension{}, nil
}

func (s *AbstractExtensionsProfileScannerService) RemoveExtensionsFromProfile(extensions []extensionscommon.IExtensionIdentifier, profileLocation basecommon.URI) error {
	// Placeholder implementation
	return nil
}

type ExtensionInstallSource string

const (
	ExtensionInstallSourceCommand      ExtensionInstallSource = "command"
	ExtensionInstallSourceSettingsSync ExtensionInstallSource = "settingsSync"
)

type InstallSource string

const (
	InstallSourceGallery  InstallSource = "gallery"
	InstallSourceVSIX     InstallSource = "vsix"
	InstallSourceResource InstallSource = "resource"
)

type SortBy string

const (
	SortByNoneOrRelevance SortBy = "NoneOrRelevance"
	SortByLastUpdatedDate SortBy = "LastUpdatedDate"
	SortByTitle           SortBy = "Title"
	SortByPublisherName   SortBy = "PublisherName"
	SortByInstallCount    SortBy = "InstallCount"
	SortByPublishedDate   SortBy = "PublishedDate"
	SortByAverageRating   SortBy = "AverageRating"
	SortByWeightedRating  SortBy = "WeightedRating"
)

type SortOrder string

const (
	SortOrderDefault    SortOrder = "default"
	SortOrderAscending  SortOrder = "ascending"
	SortOrderDescending SortOrder = "descending"
)

type FilterType string

const (
	FilterTypeTag              FilterType = "Tag"
	FilterTypeExtensionId      FilterType = "ExtensionId"
	FilterTypeCategory         FilterType = "Category"
	FilterTypeExtensionName    FilterType = "ExtensionName"
	FilterTypeTarget           FilterType = "Target"
	FilterTypeFeatured         FilterType = "Featured"
	FilterTypeSearchText       FilterType = "SearchText"
	FilterTypeExcludeWithFlags FilterType = "ExcludeWithFlags"
)

type StatisticType string

const (
	StatisticTypeInstall         StatisticType = "install"
	StatisticTypeAverageRating   StatisticType = "averagerating"
	StatisticTypeRatingCount     StatisticType = "ratingcount"
	StatisticTypeTrendingDaily   StatisticType = "trendingdaily"
	StatisticTypeTrendingWeekly  StatisticType = "trendingweekly"
	StatisticTypeTrendingMonthly StatisticType = "trendingmonthly"
)

type IProductVersion struct {
	Version string     `json:"version"`
	Date    *time.Time `json:"date,omitempty"`
}

type IGalleryExtensionProperties struct {
	Dependencies        []string                        `json:"dependencies,omitempty"`
	ExtensionPack       []string                        `json:"extensionPack,omitempty"`
	Engine              *string                         `json:"engine,omitempty"`
	EnabledAPIProposals []string                        `json:"enabledApiProposals,omitempty"`
	LocalizedLanguages  []string                        `json:"localizedLanguages,omitempty"`
	TargetPlatform      extensionscommon.TargetPlatform `json:"targetPlatform"`
	IsPreReleaseVersion bool                            `json:"isPreReleaseVersion"`
	ExecutesCode        *bool                           `json:"executesCode,omitempty"`
}

type IGalleryExtensionAsset struct {
	URI         string `json:"uri"`
	FallbackURI string `json:"fallbackUri"`
}

type IGalleryExtensionAssets struct {
	Manifest         *IGalleryExtensionAsset `json:"manifest"`
	Readme           *IGalleryExtensionAsset `json:"readme"`
	Changelog        *IGalleryExtensionAsset `json:"changelog"`
	License          *IGalleryExtensionAsset `json:"license"`
	Repository       *IGalleryExtensionAsset `json:"repository"`
	Download         IGalleryExtensionAsset  `json:"download"`
	Icon             *IGalleryExtensionAsset `json:"icon"`
	Signature        *IGalleryExtensionAsset `json:"signature"`
	CoreTranslations [][]interface{}         `json:"coreTranslations"` // [string, IGalleryExtensionAsset] pairs
}

type IGalleryExtensionIdentifier struct {
	ID   string `json:"id"`
	UUID string `json:"uuid"`
}

type IGalleryExtensionVersion struct {
	Version             string `json:"version"`
	Date                string `json:"date"`
	IsPreReleaseVersion bool   `json:"isPreReleaseVersion"`
}

type IGalleryExtension struct {
	Type                 string                            `json:"type"` // Always "gallery"
	Name                 string                            `json:"name"`
	Identifier           IGalleryExtensionIdentifier       `json:"identifier"`
	Version              string                            `json:"version"`
	DisplayName          string                            `json:"displayName"`
	PublisherID          string                            `json:"publisherId"`
	Publisher            string                            `json:"publisher"`
	PublisherDisplayName string                            `json:"publisherDisplayName"`
	PublisherDomain      interface{}                       `json:"publisherDomain,omitempty"`
	PublisherLink        *string                           `json:"publisherLink,omitempty"`
	PublisherSponsorLink *string                           `json:"publisherSponsorLink,omitempty"`
	Description          string                            `json:"description"`
	InstallCount         int64                             `json:"installCount"`
	Rating               float64                           `json:"rating"`
	RatingCount          int                               `json:"ratingCount"`
	Repository           *string                           `json:"repository,omitempty"`
	ShortDescription     *string                           `json:"shortDescription,omitempty"`
	Date                 string                            `json:"date"`
	DownloadCount        int64                             `json:"downloadCount"`
	Categories           []string                          `json:"categories"`
	Tags                 []string                          `json:"tags"`
	ReleaseNotes         *string                           `json:"releaseNotes,omitempty"`
	Versions             []IGalleryExtensionVersion        `json:"versions"`
	Properties           IGalleryExtensionProperties       `json:"properties"`
	Assets               IGalleryExtensionAssets           `json:"assets"`
	Preview              bool                              `json:"preview"`
	IsSigned             bool                              `json:"isSigned"`
	Private              bool                              `json:"private"`
	AllTargetPlatforms   []extensionscommon.TargetPlatform `json:"allTargetPlatforms"`
}

type IGalleryMetadata struct {
	ID                   string                          `json:"id"`
	PublisherDisplayName string                          `json:"publisherDisplayName"`
	PublisherID          string                          `json:"publisherId"`
	IsPreReleaseVersion  bool                            `json:"isPreReleaseVersion"`
	HasPreReleaseVersion bool                            `json:"hasPreReleaseVersion"`
	TargetPlatform       extensionscommon.TargetPlatform `json:"targetPlatform"`
	Updated              bool                            `json:"updated"`
	PreRelease           bool                            `json:"preRelease"`
	Private              bool                            `json:"private"`
	IsApplicationScoped  bool                            `json:"isApplicationScoped"`
	IsMachineScoped      bool                            `json:"isMachineScoped"`
	IsBuiltin            bool                            `json:"isBuiltin"`
	Pinned               bool                            `json:"pinned"`
	Source               InstallSource                   `json:"source"`
	Size                 int64                           `json:"size,omitempty"`
	InstalledTimestamp   *time.Time                      `json:"installedTimestamp,omitempty"`
}

type Metadata struct {
	IGalleryMetadata
	IsSystem *bool `json:"isSystem,omitempty"`
}

type ILocalExtension struct {
	extensionscommon.IExtension
	IsWorkspaceScoped    bool          `json:"isWorkspaceScoped"`
	IsMachineScoped      bool          `json:"isMachineScoped"`
	IsApplicationScoped  bool          `json:"isApplicationScoped"`
	PublisherID          *string       `json:"publisherId"`
	InstalledTimestamp   *time.Time    `json:"installedTimestamp,omitempty"`
	IsPreReleaseVersion  bool          `json:"isPreReleaseVersion"`
	HasPreReleaseVersion bool          `json:"hasPreReleaseVersion"`
	Private              bool          `json:"private"`
	PreRelease           bool          `json:"preRelease"`
	Updated              bool          `json:"updated"`
	Pinned               bool          `json:"pinned"`
	Source               InstallSource `json:"source"`
	Size                 int64         `json:"size"`
}

type IQueryOptions struct {
	Text              *string                         `json:"text,omitempty"`
	IDs               []string                        `json:"ids,omitempty"`
	Names             []string                        `json:"names,omitempty"`
	PageSize          int                             `json:"pageSize"`
	SortBy            SortBy                          `json:"sortBy"`
	SortOrder         SortOrder                       `json:"sortOrder"`
	Source            *string                         `json:"source,omitempty"`
	TargetPlatform    extensionscommon.TargetPlatform `json:"targetPlatform"`
	IncludePreRelease bool                            `json:"includePreRelease"`
}

type IDeprecationInfo struct {
	DisallowInstall bool        `json:"disallowInstall"`
	Extension       interface{} `json:"extension,omitempty"` // Can be IGalleryExtension or ILocalExtension
	Message         *string     `json:"message,omitempty"`
	AdditionalInfo  interface{} `json:"additionalInfo,omitempty"`
}

// Utility functions

func TargetPlatformToString(targetPlatform extensionscommon.TargetPlatform) string {
	switch targetPlatform {
	case extensionscommon.TargetPlatformWin32X64:
		return nls.Localize("win32-x64", "Windows 64 bit")
	case extensionscommon.TargetPlatformWin32Arm64:
		return nls.Localize("win32-arm64", "Windows ARM")
	case extensionscommon.TargetPlatformLinuxX64:
		return nls.Localize("linux-x64", "Linux 64 bit")
	case extensionscommon.TargetPlatformLinuxArm64:
		return nls.Localize("linux-arm64", "Linux ARM 64")
	case extensionscommon.TargetPlatformLinuxArmhf:
		return nls.Localize("linux-armhf", "Linux ARM")
	case extensionscommon.TargetPlatformAlpineX64:
		return nls.Localize("alpine-x64", "Alpine Linux 64 bit")
	case extensionscommon.TargetPlatformAlpineArm64:
		return nls.Localize("alpine-arm64", "Alpine ARM 64")
	case extensionscommon.TargetPlatformDarwinX64:
		return nls.Localize("darwin-x64", "Mac")
	case extensionscommon.TargetPlatformDarwinArm64:
		return nls.Localize("darwin-arm64", "Mac Silicon")
	case extensionscommon.TargetPlatformWeb:
		return nls.Localize("web", "Web")
	case extensionscommon.TargetPlatformUniversal:
		return string(extensionscommon.TargetPlatformUniversal)
	case extensionscommon.TargetPlatformUnknown:
		return string(extensionscommon.TargetPlatformUnknown)
	case extensionscommon.TargetPlatformUndefined:
		return string(extensionscommon.TargetPlatformUndefined)
	default:
		return string(extensionscommon.TargetPlatformUnknown)
	}
}

func ToTargetPlatform(targetPlatform string) extensionscommon.TargetPlatform {
	switch targetPlatform {
	case string(extensionscommon.TargetPlatformWin32X64):
		return extensionscommon.TargetPlatformWin32X64
	case string(extensionscommon.TargetPlatformWin32Arm64):
		return extensionscommon.TargetPlatformWin32Arm64
	case string(extensionscommon.TargetPlatformLinuxX64):
		return extensionscommon.TargetPlatformLinuxX64
	case string(extensionscommon.TargetPlatformLinuxArm64):
		return extensionscommon.TargetPlatformLinuxArm64
	case string(extensionscommon.TargetPlatformLinuxArmhf):
		return extensionscommon.TargetPlatformLinuxArmhf
	case string(extensionscommon.TargetPlatformAlpineX64):
		return extensionscommon.TargetPlatformAlpineX64
	case string(extensionscommon.TargetPlatformAlpineArm64):
		return extensionscommon.TargetPlatformAlpineArm64
	case string(extensionscommon.TargetPlatformDarwinX64):
		return extensionscommon.TargetPlatformDarwinX64
	case string(extensionscommon.TargetPlatformDarwinArm64):
		return extensionscommon.TargetPlatformDarwinArm64
	case string(extensionscommon.TargetPlatformWeb):
		return extensionscommon.TargetPlatformWeb
	case string(extensionscommon.TargetPlatformUniversal):
		return extensionscommon.TargetPlatformUniversal
	default:
		return extensionscommon.TargetPlatformUnknown
	}
}

func IsIExtensionIdentifier(thing interface{}) bool {
	if thing == nil {
		return false
	}

	switch v := thing.(type) {
	case IGalleryExtensionIdentifier:
		return v.ID != ""
	case extensionscommon.IExtensionIdentifier:
		return v.ID != ""
	case map[string]interface{}:
		id, ok := v["id"]
		if !ok {
			return false
		}
		idStr, ok := id.(string)
		return ok && idStr != ""
	default:
		return false
	}
}
