/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"regexp"
	"runtime"
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	extensionscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensions/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// Extension comparison functions
func AreSameExtensions(a, b extensionscommon.IExtensionIdentifier) bool {
	if a.UUID != nil && b.UUID != nil {
		return *a.UUID == *b.UUID
	}
	if a.ID == b.ID {
		return true
	}
	return strings.EqualFold(a.ID, b.ID)
}

// ExtensionKey class equivalent
var extensionKeyRegex = regexp.MustCompile(`^([^.]+\..+)-(\d+\.\d+\.\d+)(-(.+))?$`)

type ExtensionKey struct {
	Identifier     extensionscommon.IExtensionIdentifier `json:"identifier"`
	Version        string                                `json:"version"`
	TargetPlatform extensionscommon.TargetPlatform       `json:"targetPlatform"`
	ID             string                                `json:"id"`
}

func ExtensionKeyCreate(extension interface{}) *ExtensionKey {
	var version string
	var targetPlatform extensionscommon.TargetPlatform
	var identifier extensionscommon.IExtensionIdentifier

	// Handle both IExtension and IGalleryExtension
	switch ext := extension.(type) {
	case *ILocalExtension:
		version = ext.Manifest.Version
		targetPlatform = ext.TargetPlatform
		identifier = extensionscommon.IExtensionIdentifier{
			ID:   ext.Identifier.ID,
			UUID: ext.Identifier.UUID,
		}
	case *IGalleryExtension:
		version = ext.Version
		targetPlatform = ext.Properties.TargetPlatform
		// Convert IGalleryExtensionIdentifier to IExtensionIdentifier
		uuid := &ext.Identifier.UUID
		identifier = extensionscommon.IExtensionIdentifier{
			ID:   ext.Identifier.ID,
			UUID: uuid,
		}
	default:
		// Handle generic extension interfaces
		version = "1.0.0"
		targetPlatform = extensionscommon.TargetPlatformUndefined
		identifier = extensionscommon.IExtensionIdentifier{ID: "unknown", UUID: nil}
	}

	return &ExtensionKey{
		Identifier:     identifier,
		Version:        version,
		TargetPlatform: targetPlatform,
		ID:             identifier.ID,
	}
}

func ExtensionKeyParse(key string) *ExtensionKey {
	matches := extensionKeyRegex.FindStringSubmatch(key)
	if len(matches) >= 3 && matches[1] != "" && matches[2] != "" {
		targetPlatform := extensionscommon.TargetPlatformUndefined
		if len(matches) > 4 && matches[4] != "" {
			targetPlatform = extensionscommon.TargetPlatform(matches[4])
		}
		return &ExtensionKey{
			Identifier:     extensionscommon.IExtensionIdentifier{ID: matches[1]},
			Version:        matches[2],
			TargetPlatform: targetPlatform,
			ID:             matches[1],
		}
	}
	return nil
}

func (e *ExtensionKey) ToString() string {
	result := fmt.Sprintf("%s-%s", e.ID, e.Version)
	if e.TargetPlatform != extensionscommon.TargetPlatformUndefined {
		result += fmt.Sprintf("-%s", string(e.TargetPlatform))
	}
	return result
}

func (e *ExtensionKey) Equals(other interface{}) bool {
	if otherKey, ok := other.(*ExtensionKey); ok {
		return AreSameExtensions(e.Identifier, otherKey.Identifier) &&
			e.Version == otherKey.Version &&
			e.TargetPlatform == otherKey.TargetPlatform
	}
	return false
}

// Extension ID utilities
var extensionIdentifierWithVersionRegex = regexp.MustCompile(`^([^.]+\..+)@((prerelease)|(\d+\.\d+\.\d+(-.*)?))$`)

func GetIdAndVersion(id string) (string, *string) {
	matches := extensionIdentifierWithVersionRegex.FindStringSubmatch(id)
	if len(matches) > 1 && matches[1] != "" {
		version := matches[2]
		return AdoptToGalleryExtensionID(matches[1]), &version
	}
	return AdoptToGalleryExtensionID(id), nil
}

func GetExtensionID(publisher, name string) string {
	return fmt.Sprintf("%s.%s", publisher, name)
}

func AdoptToGalleryExtensionID(id string) string {
	return strings.ToLower(id)
}

func GetGalleryExtensionID(publisher *string, name string) string {
	pub := extensionscommon.UNDEFINED_PUBLISHER
	if publisher != nil {
		pub = *publisher
	}
	return AdoptToGalleryExtensionID(GetExtensionID(pub, name))
}

// Group extensions by identifier
func GroupByExtension[T any](extensions []T, getExtensionIdentifier func(T) extensionscommon.IExtensionIdentifier) [][]T {
	var byExtension [][]T

	findGroup := func(extension T) []T {
		extID := getExtensionIdentifier(extension)
		for _, group := range byExtension {
			for _, e := range group {
				if AreSameExtensions(getExtensionIdentifier(e), extID) {
					return group
				}
			}
		}
		return nil
	}

	for _, extension := range extensions {
		group := findGroup(extension)
		if group != nil {
			// Find the actual group index and append
			for i, g := range byExtension {
				if len(g) > 0 && AreSameExtensions(getExtensionIdentifier(g[0]), getExtensionIdentifier(extension)) {
					byExtension[i] = append(byExtension[i], extension)
					break
				}
			}
		} else {
			byExtension = append(byExtension, []T{extension})
		}
	}

	return byExtension
}

// Simplified telemetry data functions
type LocalExtensionTelemetryData struct {
	ID                   string  `json:"id"`
	Name                 string  `json:"name"`
	GalleryID            *string `json:"galleryId"`
	PublisherID          *string `json:"publisherId"`
	PublisherName        string  `json:"publisherName"`
	PublisherDisplayName *string `json:"publisherDisplayName"`
	Dependencies         bool    `json:"dependencies"`
}

func GetLocalExtensionTelemetryData(extension *ILocalExtension) *LocalExtensionTelemetryData {
	dependencies := extension.Manifest.ExtensionDependencies != nil && len(extension.Manifest.ExtensionDependencies) > 0
	return &LocalExtensionTelemetryData{
		ID:                   extension.Identifier.ID,
		Name:                 extension.Manifest.Name,
		GalleryID:            nil,
		PublisherID:          extension.PublisherID,
		PublisherName:        extension.Manifest.Publisher,
		PublisherDisplayName: extension.PublisherDisplayName,
		Dependencies:         dependencies,
	}
}

// Simplified gallery extension telemetry data
type GalleryExtensionTelemetryData struct {
	ID                   string  `json:"id"`
	Name                 string  `json:"name"`
	ExtensionVersion     string  `json:"extensionVersion"`
	GalleryID            *string `json:"galleryId"`
	PublisherID          *string `json:"publisherId"`
	PublisherName        string  `json:"publisherName"`
	PublisherDisplayName *string `json:"publisherDisplayName"`
	IsPreReleaseVersion  bool    `json:"isPreReleaseVersion"`
	Dependencies         bool    `json:"dependencies"`
	IsSigned             bool    `json:"isSigned"`
}

func GetGalleryExtensionTelemetryData(extension *IGalleryExtension) *GalleryExtensionTelemetryData {
	dependencies := extension.Properties.Dependencies != nil && len(extension.Properties.Dependencies) > 0

	return &GalleryExtensionTelemetryData{
		ID:                   extension.Identifier.ID,
		Name:                 extension.Name,
		ExtensionVersion:     extension.Version,
		GalleryID:            &extension.Identifier.UUID,
		PublisherID:          &extension.PublisherID,
		PublisherName:        extension.Publisher,
		PublisherDisplayName: &extension.PublisherDisplayName,
		IsPreReleaseVersion:  extension.Properties.IsPreReleaseVersion,
		Dependencies:         dependencies,
		IsSigned:             extension.IsSigned,
	}
}

// Extension dependencies
func GetExtensionDependencies(installedExtensions []*extensionscommon.IExtension, extension *extensionscommon.IExtension) []*extensionscommon.IExtension {
	var dependencies []*extensionscommon.IExtension
	var extensions []string

	if extension.Manifest.ExtensionDependencies != nil {
		extensions = make([]string, len(extension.Manifest.ExtensionDependencies))
		copy(extensions, extension.Manifest.ExtensionDependencies)
	}

	for len(extensions) > 0 {
		id := extensions[0]
		extensions = extensions[1:]

		if id != "" {
			// Check if not already in dependencies
			found := false
			for _, dep := range dependencies {
				if AreSameExtensions(dep.Identifier, extensionscommon.IExtensionIdentifier{ID: id}) {
					found = true
					break
				}
			}

			if !found {
				// Find extension in installed extensions
				var matchingExtensions []*extensionscommon.IExtension
				for _, ext := range installedExtensions {
					if AreSameExtensions(ext.Identifier, extensionscommon.IExtensionIdentifier{ID: id}) {
						matchingExtensions = append(matchingExtensions, ext)
					}
				}

				if len(matchingExtensions) == 1 {
					dependencies = append(dependencies, matchingExtensions[0])
					if matchingExtensions[0].Manifest.ExtensionDependencies != nil {
						extensions = append(extensions, matchingExtensions[0].Manifest.ExtensionDependencies...)
					}
				}
			}
		}
	}

	return dependencies
}

// Platform detection
func isAlpineLinux(fileService filescommon.IFileService, logService logcommon.ILogService) (bool, error) {
	if runtime.GOOS != "linux" {
		return false, nil
	}

	var content string

	// Try /etc/os-release first
	osReleaseURI := basecommon.FileURI("/etc/os-release")
	fileContent, err := fileService.ReadFile(osReleaseURI, nil, basecommon.CancellationTokenNone)
	if err != nil {
		// Try /usr/lib/os-release as fallback
		osReleaseURI = basecommon.FileURI("/usr/lib/os-release")
		fileContent, err = fileService.ReadFile(osReleaseURI, nil, basecommon.CancellationTokenNone)
		if err != nil {
			logService.Debug("Error while getting the os-release file.", err.Error())
			return false, nil
		}
	}

	content = fileContent.Value.ToString()
	idRegex := regexp.MustCompile(`^ID=([^\r\n]*)`)
	matches := idRegex.FindStringSubmatch(content)

	return len(matches) > 1 && matches[1] == "alpine", nil
}

func ComputeTargetPlatform(fileService filescommon.IFileService, logService logcommon.ILogService) (extensionscommon.TargetPlatform, error) {
	alpineLinux, err := isAlpineLinux(fileService, logService)
	if err != nil {
		logService.Debug("Error checking for Alpine Linux:", err.Error())
	}

	platformStr := runtime.GOOS
	if alpineLinux {
		platformStr = "alpine"
	}

	targetPlatform := GetTargetPlatform(platformStr, runtime.GOARCH)
	logService.Debug("ComputeTargetPlatform:", string(targetPlatform))
	return targetPlatform, nil
}

// Malicious extension detection
func IsMalicious(identifier extensionscommon.IExtensionIdentifier, malicious []*MaliciousExtensionInfo) bool {
	return FindMatchingMaliciousEntry(identifier, malicious) != nil
}

func FindMatchingMaliciousEntry(identifier extensionscommon.IExtensionIdentifier, malicious []*MaliciousExtensionInfo) *MaliciousExtensionInfo {
	for _, entry := range malicious {
		if entry.ExtensionOrPublisher.Type == "string" {
			publisherPart := strings.Split(identifier.ID, ".")[0]
			if strings.EqualFold(publisherPart, entry.ExtensionOrPublisher.Value) {
				return entry
			}
		} else if entry.ExtensionOrPublisher.Type == "extension" {
			if AreSameExtensions(identifier, entry.ExtensionOrPublisher.Extension) {
				return entry
			}
		}
	}
	return nil
}

// Target platform compatibility check
func IsTargetPlatformCompatible(targetPlatform extensionscommon.TargetPlatform, allTargetPlatforms []extensionscommon.TargetPlatform, currentPlatform extensionscommon.TargetPlatform) bool {
	// Universal extensions work on any platform
	if targetPlatform == extensionscommon.TargetPlatformUniversal {
		return true
	}

	// Exact match
	if targetPlatform == currentPlatform {
		return true
	}

	// Platform-specific compatibility logic
	currentPlatformBase := strings.Split(string(currentPlatform), "-")[0]
	targetPlatformBase := strings.Split(string(targetPlatform), "-")[0]

	// Same base platform (e.g., linux-x64 compatible with linux-arm64 in some cases)
	if currentPlatformBase == targetPlatformBase {
		return true
	}

	return false
}

// Helper function to get target platform for current system
func GetTargetPlatform(platformName, arch string) extensionscommon.TargetPlatform {
	switch platformName {
	case "windows":
		switch arch {
		case "amd64":
			return extensionscommon.TargetPlatformWin32X64
		case "arm64":
			return extensionscommon.TargetPlatformWin32Arm64
		case "386":
			return extensionscommon.TargetPlatformWin32Ia32
		default:
			return extensionscommon.TargetPlatformWin32X64
		}
	case "linux":
		switch arch {
		case "amd64":
			return extensionscommon.TargetPlatformLinuxX64
		case "arm64":
			return extensionscommon.TargetPlatformLinuxArm64
		case "arm":
			return extensionscommon.TargetPlatformLinuxArmhf
		default:
			return extensionscommon.TargetPlatformLinuxX64
		}
	case "alpine":
		switch arch {
		case "amd64":
			return extensionscommon.TargetPlatformAlpineX64
		case "arm64":
			return extensionscommon.TargetPlatformAlpineArm64
		default:
			return extensionscommon.TargetPlatformAlpineX64
		}
	case "darwin":
		switch arch {
		case "amd64":
			return extensionscommon.TargetPlatformDarwinX64
		case "arm64":
			return extensionscommon.TargetPlatformDarwinArm64
		default:
			return extensionscommon.TargetPlatformDarwinX64
		}
	case "web":
		return extensionscommon.TargetPlatformWeb
	default:
		return extensionscommon.TargetPlatformUniversal
	}
}
