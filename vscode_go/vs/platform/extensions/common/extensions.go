/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

const (
	USER_MANIFEST_CACHE_FILE    = "extensions.user.cache"
	BUILTIN_MANIFEST_CACHE_FILE = "extensions.builtin.cache"
	UNDEFINED_PUBLISHER         = "undefined_publisher"
)

type ExtensionType int

const (
	ExtensionTypeSystem ExtensionType = iota
	ExtensionTypeUser
)

type TargetPlatform string

const (
	TargetPlatformWin32X64   TargetPlatform = "win32-x64"
	TargetPlatformWin32Arm64 TargetPlatform = "win32-arm64"
	TargetPlatformWin32Ia32  TargetPlatform = "win32-ia32"

	TargetPlatformLinuxX64   TargetPlatform = "linux-x64"
	TargetPlatformLinuxArm64 TargetPlatform = "linux-arm64"
	TargetPlatformLinuxArmhf TargetPlatform = "linux-armhf"

	TargetPlatformAlpineX64   TargetPlatform = "alpine-x64"
	TargetPlatformAlpineArm64 TargetPlatform = "alpine-arm64"

	TargetPlatformDarwinX64   TargetPlatform = "darwin-x64"
	TargetPlatformDarwinArm64 TargetPlatform = "darwin-arm64"

	TargetPlatformWeb TargetPlatform = "web"

	TargetPlatformUniversal TargetPlatform = "universal"
	TargetPlatformUnknown   TargetPlatform = "unknown"
	TargetPlatformUndefined TargetPlatform = "undefined"
)

type IExtensionIdentifier struct {
	ID   string  `json:"id"`
	UUID *string `json:"uuid,omitempty"`
}

type ICommand struct {
	Command  string      `json:"command"`
	Title    interface{} `json:"title"` // Can be string or ILocalizedString
	Category interface{} `json:"category,omitempty"`
}

type IExtensionContributions struct {
	Commands    []ICommand    `json:"commands,omitempty"`
	Keybindings []interface{} `json:"keybindings,omitempty"`
	Languages   []interface{} `json:"languages,omitempty"`
	Grammars    []interface{} `json:"grammars,omitempty"`
	Themes      []interface{} `json:"themes,omitempty"`
	// Add other contribution types as needed
}

type IExtensionManifest struct {
	Name                  string                   `json:"name"`
	DisplayName           *string                  `json:"displayName,omitempty"`
	Publisher             string                   `json:"publisher"`
	Version               string                   `json:"version"`
	Engines               map[string]string        `json:"engines"`
	Description           *string                  `json:"description,omitempty"`
	Main                  *string                  `json:"main,omitempty"`
	Icon                  *string                  `json:"icon,omitempty"`
	Contributes           *IExtensionContributions `json:"contributes,omitempty"`
	ActivationEvents      []string                 `json:"activationEvents,omitempty"`
	Keywords              []string                 `json:"keywords,omitempty"`
	Categories            []string                 `json:"categories,omitempty"`
	ExtensionDependencies []string                 `json:"extensionDependencies,omitempty"`
	ExtensionPack         []string                 `json:"extensionPack,omitempty"`
	Repository            interface{}              `json:"repository,omitempty"`
	Bugs                  interface{}              `json:"bugs,omitempty"`
	Homepage              *string                  `json:"homepage,omitempty"`
	License               *string                  `json:"license,omitempty"`
	EnableProposedAPI     bool                     `json:"enableProposedApi,omitempty"`
	Preview               bool                     `json:"preview,omitempty"`
}

type IExtension struct {
	Type                 ExtensionType        `json:"type"`
	IsBuiltin            bool                 `json:"isBuiltin"`
	Identifier           IExtensionIdentifier `json:"identifier"`
	Manifest             IExtensionManifest   `json:"manifest"`
	Location             basecommon.URI       `json:"location"`
	TargetPlatform       TargetPlatform       `json:"targetPlatform"`
	PublisherDisplayName *string              `json:"publisherDisplayName,omitempty"`
	ReadmeURL            *basecommon.URI      `json:"readmeUrl,omitempty"`
	ChangelogURL         *basecommon.URI      `json:"changelogUrl,omitempty"`
	IsValid              bool                 `json:"isValid"`
	Validations          []ValidationMessage  `json:"validations"`
	PreRelease           bool                 `json:"preRelease"`
}

type ValidationMessage struct {
	Severity int    `json:"severity"`
	Message  string `json:"message"`
}

// Extension helper functions

func (e *IExtension) GetDisplayName() string {
	if e.Manifest.DisplayName != nil {
		return *e.Manifest.DisplayName
	}
	return e.Manifest.Name
}

func (e *IExtension) GetPublisher() string {
	return e.Manifest.Publisher
}

func (e *IExtension) GetVersion() string {
	return e.Manifest.Version
}

func (e *IExtension) GetID() string {
	return e.Identifier.ID
}

func (e *IExtension) GetUUID() *string {
	return e.Identifier.UUID
}

// Target platform helper functions

func GetCurrentTargetPlatform() TargetPlatform {
	// This would be implemented based on runtime.GOOS and runtime.GOARCH
	// For now, return a default value
	return TargetPlatformUniversal
}

func IsTargetPlatformCompatible(platform TargetPlatform) bool {
	current := GetCurrentTargetPlatform()
	return platform == TargetPlatformUniversal || platform == current
}

// ExtensionIdentifierToKey converts an extension identifier to a key string
func ExtensionIdentifierToKey(id string) string {
	return strings.ToLower(id)
}

// NewExtensionIdentifier creates a new extension identifier
func NewExtensionIdentifier(id string) IExtensionIdentifier {
	return IExtensionIdentifier{
		ID: id,
	}
}
