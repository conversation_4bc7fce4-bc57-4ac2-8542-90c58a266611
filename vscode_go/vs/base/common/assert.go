/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"runtime"
)

// BugIndicatingError represents an error that indicates a bug in the code
type BugIndicatingError struct {
	message string
	stack   string
}

func (e *BugIndicatingError) Error() string {
	return e.message
}

// NewBugIndicatingError creates a new BugIndicatingError with stack trace
func NewBugIndicatingError(message string) *BugIndicatingError {
	// Capture stack trace
	buf := make([]byte, 1024)
	n := runtime.Stack(buf, false)
	stack := string(buf[:n])

	return &BugIndicatingError{
		message: message,
		stack:   stack,
	}
}

// Ok throws an error with the provided message if the provided value does not evaluate to true
// Deprecated: Use Assert(...) instead
func Ok(value bool, message ...string) {
	if !value {
		msg := "Assertion Failed"
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion failed (%s)", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
}

// AssertNever asserts that a value is never reached
func AssertNever(value interface{}, message ...string) {
	msg := "Unreachable"
	if len(message) > 0 && message[0] != "" {
		msg = message[0]
	}
	panic(NewBugIndicatingError(msg))
}

// Assert asserts that a condition is truthy
// Throws provided messageOrError if the condition is falsy
func Assert(condition bool, messageOrError ...interface{}) {
	if !condition {
		var err error

		if len(messageOrError) > 0 {
			switch v := messageOrError[0].(type) {
			case string:
				err = NewBugIndicatingError(fmt.Sprintf("Assertion Failed: %s", v))
			case error:
				err = v
			default:
				err = NewBugIndicatingError(fmt.Sprintf("Assertion Failed: %v", v))
			}
		} else {
			err = NewBugIndicatingError("Assertion Failed: unexpected state")
		}

		panic(err)
	}
}

// SoftAssert is like assert, but doesn't throw - it logs the error instead
func SoftAssert(condition bool, message ...string) {
	if !condition {
		msg := "Soft Assertion Failed"
		if len(message) > 0 && message[0] != "" {
			msg = message[0]
		}
		OnUnexpectedError(NewBugIndicatingError(msg))
	}
}

// AssertFn asserts a condition function (condition must be side-effect free!)
func AssertFn(condition func() bool) {
	if !condition() {
		// Reevaluate condition again to make debugging easier
		condition()
		OnUnexpectedError(NewBugIndicatingError("Assertion Failed"))
	}
}

// OnUnexpectedError handles unexpected errors (logging, reporting, etc.)
var OnUnexpectedError = func(e interface{}) {
	// Default implementation: print to stderr
	fmt.Printf("Unexpected error: %v\n", e)

	// In a real implementation, this might:
	// - Log to a file
	// - Send to error reporting service
	// - Show user notification
	// - etc.
}

// AssertType asserts that a value is of a specific type
func AssertType[T any](value interface{}) T {
	if v, ok := value.(T); ok {
		return v
	}

	var zero T
	panic(NewBugIndicatingError(fmt.Sprintf("Type assertion failed: expected %T, got %T", zero, value)))
}

// AssertNotNil asserts that a pointer is not nil
func AssertNotNil[T any](ptr *T, message ...string) *T {
	if ptr == nil {
		msg := "Assertion Failed: pointer is nil"
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
	return ptr
}

// AssertNotEmpty asserts that a string is not empty
func AssertNotEmpty(str string, message ...string) string {
	if str == "" {
		msg := "Assertion Failed: string is empty"
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
	return str
}

// AssertNotEmptySlice asserts that a slice is not empty
func AssertNotEmptySlice[T any](slice []T, message ...string) []T {
	if len(slice) == 0 {
		msg := "Assertion Failed: slice is empty"
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
	return slice
}

// AssertInRange asserts that a value is within a specific range
func AssertInRange[T interface {
	~int | ~int8 | ~int16 | ~int32 | ~int64 | ~uint | ~uint8 | ~uint16 | ~uint32 | ~uint64 | ~float32 | ~float64
}](
	value, min, max T, message ...string) T {
	if value < min || value > max {
		msg := fmt.Sprintf("Assertion Failed: value %v is not in range [%v, %v]", value, min, max)
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
	return value
}

// AssertEqual asserts that two values are equal
func AssertEqual[T comparable](expected, actual T, message ...string) {
	if expected != actual {
		msg := fmt.Sprintf("Assertion Failed: expected %v, got %v", expected, actual)
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
}

// AssertNotEqual asserts that two values are not equal
func AssertNotEqual[T comparable](unexpected, actual T, message ...string) {
	if unexpected == actual {
		msg := fmt.Sprintf("Assertion Failed: values should not be equal: %v", actual)
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
}

// AssertTrue asserts that a value is true
func AssertTrue(value bool, message ...string) {
	if !value {
		msg := "Assertion Failed: expected true"
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
}

// AssertFalse asserts that a value is false
func AssertFalse(value bool, message ...string) {
	if value {
		msg := "Assertion Failed: expected false"
		if len(message) > 0 && message[0] != "" {
			msg = fmt.Sprintf("Assertion Failed: %s", message[0])
		}
		panic(NewBugIndicatingError(msg))
	}
}

// AssertContains asserts that a slice contains a specific element
func AssertContains[T comparable](slice []T, element T, message ...string) {
	for _, v := range slice {
		if v == element {
			return
		}
	}

	msg := fmt.Sprintf("Assertion Failed: slice does not contain %v", element)
	if len(message) > 0 && message[0] != "" {
		msg = fmt.Sprintf("Assertion Failed: %s", message[0])
	}
	panic(NewBugIndicatingError(msg))
}

// AssertNotContains asserts that a slice does not contain a specific element
func AssertNotContains[T comparable](slice []T, element T, message ...string) {
	for _, v := range slice {
		if v == element {
			msg := fmt.Sprintf("Assertion Failed: slice should not contain %v", element)
			if len(message) > 0 && message[0] != "" {
				msg = fmt.Sprintf("Assertion Failed: %s", message[0])
			}
			panic(NewBugIndicatingError(msg))
		}
	}
}

// Fail unconditionally fails with a message
func Fail(message ...string) {
	msg := "Assertion Failed"
	if len(message) > 0 && message[0] != "" {
		msg = fmt.Sprintf("Assertion Failed: %s", message[0])
	}
	panic(NewBugIndicatingError(msg))
}

// Try executes a function and returns any panic as an error
func Try(fn func()) (err error) {
	defer func() {
		if r := recover(); r != nil {
			switch v := r.(type) {
			case error:
				err = v
			default:
				err = fmt.Errorf("panic: %v", v)
			}
		}
	}()

	fn()
	return nil
}

// Must executes a function that returns a value and an error, panicking if there's an error
func Must[T any](value T, err error) T {
	if err != nil {
		panic(err)
	}
	return value
}

// MustNot executes a function that returns an error, panicking if there's an error
func MustNot(err error) {
	if err != nil {
		panic(err)
	}
}
